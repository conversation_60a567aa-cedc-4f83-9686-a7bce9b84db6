

#include "zexuan/net/event_loop_thread.hpp"
#include "zexuan/net/tcp_client.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/base/noncopyable.hpp"
#include <iostream>
#include <stdio.h>
#include <unistd.h>
#include <thread>
#include <chrono>
#include <signal.h>
#include <spdlog/spdlog.h>

using namespace zexuan;
using namespace zexuan::net;
using namespace zexuan::platform::network;

class ChatClient : zexuan::base::noncopyable
{
 public:
  ChatClient(EventLoop* loop, const Address& serverAddr)
    : client_(loop, serverAddr, "ChatClient")
  {
    client_.setConnectionCallback(
        std::bind(&ChatClient::onConnection, this, std::placeholders::_1));
    client_.setMessageCallback(
        std::bind(&ChatClient::onMessage, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));
    client_.enableRetry();
  }

  void connect()
  {
    client_.connect();
  }

  void disconnect()
  {
    client_.disconnect();
  }

  void write(const std::string& message)
  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (connection_)
    {
      connection_->send(message);
    }
  }

 private:
  void onConnection(const TcpConnectionPtr& conn)
  {
    spdlog::info("{} -> {} is {}", 
                 conn->localAddress().toIpPort(),
                 conn->peerAddress().toIpPort(),
                 conn->connected() ? "UP" : "DOWN");

    std::lock_guard<std::mutex> lock(mutex_);
    if (conn->connected())
    {
      connection_ = conn;
    }
    else
    {
      connection_.reset();
    }
  }

  void onMessage(const TcpConnectionPtr& conn,
                 Buffer* buf,
                 std::chrono::system_clock::time_point)
  {
    while (buf->readableBytes() > 0)
    {
      std::string msg(buf->peek(), buf->readableBytes());
      printf("<<< %s\n", msg.c_str());
      buf->retrieveAll();
    }
  }

  TcpClient client_;
  std::mutex mutex_;
  TcpConnectionPtr connection_;
};

int main(int argc, char* argv[])
{
  spdlog::info("pid = {}", getpid());
  
  if (argc > 2)
  {
    EventLoopThread loopThread;
    EventLoop* loop = loopThread.startLoop();
    
    uint16_t port = static_cast<uint16_t>(atoi(argv[2]));
    Address serverAddr(argv[1], port);

    ChatClient client(loop, serverAddr);
    client.connect();
    spdlog::info("ChatClient连接成功，输入消息或按Ctrl+C退出");
    
    std::string line;
    while (std::getline(std::cin, line))
    {
      client.write(line);
    }
    client.disconnect();
    std::this_thread::sleep_for(std::chrono::seconds(1));  // wait for disconnect
  }
  else
  {
    printf("Usage: %s host_ip port\n", argv[0]);
  }
}
