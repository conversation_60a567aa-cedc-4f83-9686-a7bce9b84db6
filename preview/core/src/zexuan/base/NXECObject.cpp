/**********************************************************************
* NXECObject.cpp         author:jjl      date:28/08/2013            
*---------------------------------------------------------------------
*  note: 基础对象实现文件，移植到新的base模块                                                              
*  
**********************************************************************/

#include "zexuan/base/NXECObject.h"
#include <iostream>
#include <sstream>
#include <chrono>

namespace zexuan {
namespace base {

// 静态线程池初始化
std::shared_ptr<zexuan::ThreadPool> CNXECObject::s_threadPool = nullptr;

/**
* @brief         构造函数
* @param[in]     const std::string& loggerName: 日志记录器名称
* @param[in]     const char* pClassName: 类名
* @param[out]    无
* @return        无
*/
CNXECObject::CNXECObject(const std::string& loggerName, const char* pClassName)
    : m_loggerName(loggerName), m_strCurClassName(pClassName ? pClassName : "CNXECObject")
{
    // 初始化线程池（如果还没有初始化）
    if (!s_threadPool) {
        s_threadPool = std::make_shared<zexuan::ThreadPool>(4); // 默认4个线程
    }
    
    m_strRunTimeObjName.clear();
    m_ThreadList.clear();
    
    // 获取或创建logger
    auto logger = spdlog::get(m_loggerName);
    if (!logger) {
        try {
            logger = spdlog::basic_logger_mt(m_loggerName, "logs/" + m_loggerName + ".log");
            logger->set_level(spdlog::level::debug);
            logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v");
        } catch (const std::exception& e) {
            // 如果创建失败，使用默认logger
            logger = spdlog::default_logger();
        }
    }
}

/**
* @brief         析构函数
* @param[in]     无 
* @param[out]    无
* @return        无
*/
CNXECObject::~CNXECObject()
{
    // 停止所有线程
    StopAllThread();
    m_ThreadList.clear();
}

/**
* @brief         记录错误日志
* @param[in]     const char * cLog:日志内容
* @return        void
*/
void CNXECObject::RecordErrorLog(const char* cLog)
{
    if (cLog == nullptr) {
        return;
    }

    auto logger = spdlog::get(m_loggerName);
    if (!logger) {
        logger = spdlog::default_logger();
    }

    // 格式化日志
    std::string strLog;
    __FormatLogString(cLog, strLog);
    
    logger->error(strLog);
}

/**
* @brief         记录跟踪日志
* @param[in]     const char * cLog:日志内容
* @return        void
*/
void CNXECObject::RecordTraceLog(const char* cLog)
{
    if (cLog == nullptr) {
        return;
    }

    auto logger = spdlog::get(m_loggerName);
    if (!logger) {
        logger = spdlog::default_logger();
    }

    // 格式化日志
    std::string strLog;
    __FormatLogString(cLog, strLog);
    
    logger->debug(strLog);
}

/*
*  @brief      格式化日志字符串
*  @param[in] 	const char * cLog:原字符串
*  @param[out] std::string& strLog;格式化后的日志字符串
*  @return 	void
*/
void CNXECObject::__FormatLogString(const char* cLog, std::string& strLog)
{
    // 如果运行时名称不为空
    if (!m_strRunTimeObjName.empty()) {
        strLog = "[RUNOBJ:" + m_strRunTimeObjName + "]->";
    }

    strLog = strLog + "[CLASS:" + m_strCurClassName + "] ";
    strLog += cLog; 
}

/**
* @brief         记录错误日志并附加父类函数执行的错误日志
* @param[in]     const char * cLog:日志内容
* @param[in]     const char* cPClass:父类名称
* @return        void
*/
void CNXECObject::RcdErrLogWithParentClass(const char* cLog, const char* cPClass)
{
    std::string strLog = cLog;
    strLog += "[PARENT CLASS:";
    strLog += cPClass;
    strLog += "]";
    
    RecordErrorLog(strLog.c_str());
}

/**
* @brief         记录跟踪日志并附加父类函数执行的跟踪日志
* @param[in]     const char * cLog:日志内容
* @param[in]     const char* cPClass:父类名称
* @return        void
*/
void CNXECObject::RcdTrcLogWithParentClass(const char* cLog, const char* cPClass)
{
    std::string strLog = cLog;
    strLog += "[PARENT CLASS:";
    strLog += cPClass;
    strLog += "]";
    
    RecordTraceLog(strLog.c_str());
}

/**
* @brief         设置日志记录器
* @param[in]     const std::string& loggerName: 日志记录器名称
* @return        void
*/
void CNXECObject::SetLogRecord(const std::string& loggerName)
{
    m_loggerName = loggerName;
}

/*
*  @brief   	设置运行时的对象名称
*  @param[IN]  const char * chObjName:运行时的实例对象名称 		
*  @return 	bool true-成功 false-失败
*/
bool CNXECObject::SetRunTimeObjName(const char* chObjName)
{
    if (chObjName) {
        m_strRunTimeObjName = chObjName;
    }
    return true;
}

/**
* @brief         设置日志类名称
* @param[in]     const char * cClassName 类名称
* @param[out]    无
* @return        void
*/
void CNXECObject::_SetLogClassName(const char* cClassName)
{
    if (cClassName) {
        m_strCurClassName = cClassName;
    }
}

/**
* @brief         添加线程信息
* @param[in]     EC_THREAD_INFO * pThreadInfo:线程信息结构
* @return        bool true-成功 false-失败
*/
bool CNXECObject::AddThreadInfo(EC_THREAD_INFO* pThreadInfo)
{
    if (pThreadInfo) {
        m_ThreadList.push_back(std::unique_ptr<EC_THREAD_INFO>(pThreadInfo));
        return true;
    }
    return false;
}

/**
* @brief         启动所有线程
* @param[in]     无
* @return        bool true-成功 false-失败
*/
bool CNXECObject::StartAllThread()
{
    std::string strError;
    
    for (auto& pThreadInfo : m_ThreadList) {
        if (!pThreadInfo) {
            continue;
        }

        if (pThreadInfo->nState == T_S_START) {
            // 已经启动的不重复启动
            continue;
        }
        
        if (!StartOneThread(pThreadInfo.get())) {
            strError = "启动" + pThreadInfo->strThreadDes + "线程失败";
            RcdErrLogWithParentClass(strError.c_str(), "CNXEcObject");
            return false;
        }
    }
    
    strError = "StartAllThread()：全部线程(" + std::to_string(m_ThreadList.size()) + "个)启动成功";
    RcdTrcLogWithParentClass(strError.c_str(), "CNXEcObject");
    
    return true;
}

/**
* @brief         启动单个指定的线程
* @param[in]     EC_THREAD_INFO * pThreadInfo:线程结构
* @return        bool true-成功 false-失败
*/
bool CNXECObject::StartOneThread(EC_THREAD_INFO* pThreadInfo)
{
    if (!pThreadInfo) {
        RcdTrcLogWithParentClass("StartOneThread()时，参数为NULL", "CNXEcObject");
        return false;
    }

    if (!pThreadInfo->pCallBackFunc) {
        std::string strError = "StartOneThread()时，" + pThreadInfo->strThreadDes + "线程回调函数为NULL";
        RcdErrLogWithParentClass(strError.c_str(), "CNXEcObject");
        return false;
    }

    try {
        // 使用线程池提交任务
        pThreadInfo->threadFuture = s_threadPool->commit([pThreadInfo]() -> int {
            try {
                return pThreadInfo->pCallBackFunc(pThreadInfo->pSelfObj, pThreadInfo->pParam);
            } catch (const std::exception& e) {
                spdlog::error("线程{}执行异常: {}", pThreadInfo->strThreadDes, e.what());
                return -1;
            } catch (...) {
                spdlog::error("线程{}执行未知异常", pThreadInfo->strThreadDes);
                return -1;
            }
        });

        pThreadInfo->nState = T_S_START;
        std::string strSuccess = "启动" + pThreadInfo->strThreadDes + "线程成功";
        RcdTrcLogWithParentClass(strSuccess.c_str(), "CNXEcObject");
        
        return true;
    } catch (const std::exception& e) {
        std::string strError = "启动" + pThreadInfo->strThreadDes + "线程失败: " + e.what();
        RcdErrLogWithParentClass(strError.c_str(), "CNXEcObject");
        return false;
    }
}

/**
* @brief         停止所有线程
* @param[in]     无
* @return        bool true-成功 false-失败
*/
bool CNXECObject::StopAllThread()
{
    for (auto& pThreadInfo : m_ThreadList) {
        if (!pThreadInfo) {
            continue;
        }

        // 等待线程完成
        if (pThreadInfo->nState != T_S_STOP && pThreadInfo->threadFuture.valid()) {
            try {
                // 等待线程完成，设置超时时间
                auto status = pThreadInfo->threadFuture.wait_for(std::chrono::seconds(5));
                if (status == std::future_status::ready) {
                    int result = pThreadInfo->threadFuture.get();
                    std::string strSuccess = "停止" + pThreadInfo->strThreadDes + "线程成功";
                    RcdTrcLogWithParentClass(strSuccess.c_str(), "CNXEcObject");
                } else {
                    std::string strError = "停止" + pThreadInfo->strThreadDes + "线程超时";
                    RcdErrLogWithParentClass(strError.c_str(), "CNXEcObject");
                }
            } catch (const std::exception& e) {
                std::string strError = "停止" + pThreadInfo->strThreadDes + "线程异常: " + e.what();
                RcdErrLogWithParentClass(strError.c_str(), "CNXEcObject");
            }
        }
        
        pThreadInfo->nState = T_S_STOP;
    }
    
    return true;
}

} // namespace base
} // namespace zexuan
