/**********************************************************************
* NXECObject.h         author:jjl      date:28/08/2013            
*---------------------------------------------------------------------
*  note: 基础对象头文件，移植到新的base模块                                                              
*  
**********************************************************************/

#ifndef _H_NXECOBJECT_H_ 
#define _H_NXECOBJECT_H_

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <future>
#include <spdlog/spdlog.h>
#include "zexuan/thread_pool.hpp"

namespace zexuan {
namespace base {

/** @brief         线程状态枚举*/
enum EC_THREAD_STATE
{
    T_S_STOP     = 0,      /**<      停止 */
    T_S_START    = 1,      /**<      启动 */
    T_S_SUSPEND  = 2,      /**<      暂停 */
};

/** @brief         线程回调函数指针*/
typedef std::function<int(void* pObj, void* pParam)> PFUNC_THREAD_CALLBACK;

/** @brief                 线程信息结构*/
typedef struct _EC_THREAD_INFO
{
    /** @brief         线程句柄*/
    std::thread::id threadId;

    /** @brief         线程内部函数执行后的返回值(内部的静态成员函数,如SY_THREAD_FUNCTION类型函数)*/
    PFUNC_THREAD_CALLBACK pCallBackFunc;

    /** @brief         对象指针*/
    void* pSelfObj;   

    /** @brief         附加参数(为NULL,表示不需要附加参数)*/
    void* pParam;

    /** @brief         线程描述(格式："类名+函数名称")*/
    std::string strThreadDes;

    /** @brief         线程状态*/
    EC_THREAD_STATE nState;	

    /** @brief         线程future对象*/
    std::future<int> threadFuture;

    _EC_THREAD_INFO()
    {
        pCallBackFunc = nullptr;
        threadId = std::thread::id{};
        strThreadDes = "";
        pSelfObj = nullptr;
        pParam = nullptr;
        nState = T_S_STOP;
    }

} EC_THREAD_INFO;

/**
* @defgroup   CNXECObject 通用对象类
* @{
*/
 
/**
 * @brief      实现所有对象的公共操作，包括日志记录
 * <AUTHOR>
 * @date       28/08/2013
 *
 * example
 * @code*  
 *   
 *
 * @endcode
 */

class CNXECObject
{
    ///////////////////////////////////////////////////////////////构造、析构
protected:

    /**
    * @brief         构造函数
    * @param[in]     const std::string& loggerName: 日志记录器名称
    * @param[in]     const char * pClassName: 类名
    * @param[out]    无
    * @return        无
    */
    CNXECObject(const std::string& loggerName, const char* pClassName);

public:

    /**
    * @brief         析构函数
    * @param[in]     无 
    * @param[out]    无
    * @return        无
    */
    virtual ~CNXECObject();

    ///////////////////////////////////////////////////////////////公共方法
public:

    /**
    * @brief         记录错误日志
    * @param[in]     const char * cLog:日志内容
    * @return        void
    */
    virtual void RecordErrorLog(const char* cLog);

    /**
    * @brief         记录跟踪日志
    * @param[in]     const char * cLog:日志内容
    * @return        void
    */
    virtual void RecordTraceLog(const char* cLog);

    /**
    * @brief         记录错误日志并附加父类函数执行的错误日志
    * @param[in]     const char * cLog:日志内容
    * @param[in]     const char* cPClass:父类名称
    * @return        void
    */
    virtual void RcdErrLogWithParentClass(const char* cLog, const char* cPClass);

    /**
    * @brief         记录跟踪日志并附加父类函数执行的跟踪日志
    * @param[in]     const char * cLog:日志内容
    * @param[in]     const char* cPClass:父类名称
    * @return        void
    */
    virtual void RcdTrcLogWithParentClass(const char* cLog, const char* cPClass);

    /**
    * @brief         设置日志记录器
    * @param[in]     const std::string& loggerName: 日志记录器名称
    * @return        void
    */
    virtual void SetLogRecord(const std::string& loggerName);

     /*
     *  @brief   	设置运行时的对象名称
     *  @param[IN]  const char * chObjName:运行时的实例对象名称 		
     *  @return 	bool true-成功 false-失败
     */
     virtual bool SetRunTimeObjName(const char* chObjName);

    ///////////////////////////////////////////////////////////////线程管理
protected:

    /**
    * @brief         添加线程信息
    * @param[in]     EC_THREAD_INFO * ThreadInfo:线程信息结构
    * @return        bool true-成功 false-失败
    */
    virtual bool AddThreadInfo(EC_THREAD_INFO* pThreadInfo);

    /**
    * @brief         启动所有线程
    * @param[in]     无
    * @return        bool true-成功 false-失败
    */
    virtual bool StartAllThread();

    /**
    * @brief         启动单个指定的线程,
    * @param[in]     EC_THREAD_INFO * pThreadInfo:线程结构
    * @return        bool true-成功 false-失败
    */
    virtual bool StartOneThread(EC_THREAD_INFO* pThreadInfo);

    /**
    * @brief         停止所有线程
    * @param[in]     无
    * @return        bool true-成功 false-失败
    */
    virtual bool StopAllThread();

    /**
    * @brief         设置日志类名称
    * @param[in]     const char * cClassName 类名称
    * @param[out]    无
    * @return        void
    */
     void _SetLogClassName(const char* cClassName);

    //////////////////////////////////////////////////////////////私有方法
private:

    /*
     *  @brief      格式化日志字符串
     *  @param[in] 	const char * cLog:原字符串
	 *  @param[out] std::string& strLog;格式化后的日志字符串
     *  @return 	void
     */
    void __FormatLogString(const char* cLog, std::string& strLog);

    ///////////////////////////////////////////////////////////////保护成员
protected:

    /** @brief             日志记录器名称*/
    std::string m_loggerName;

    /** @brief             线程池指针*/
    static std::shared_ptr<zexuan::ThreadPool> s_threadPool;

    ///////////////////////////////////////////////////////////////私有成员
private:

    /** @brief         当前类名称*/
    std::string m_strCurClassName;

    /** @brief         运行时对象名称*/
    std::string m_strRunTimeObjName;

    /** @brief         线程信息队列*/
    std::vector<std::unique_ptr<EC_THREAD_INFO>> m_ThreadList;
};

/** @} */ //OVER
} // namespace base
} // namespace zexuan

#endif
