# Poller 重构改进

## 问题分析

原来的平台库设计存在以下问题：

1. **命名空间混乱**：poller实现在`zexuan::platform::network`命名空间，但基类在`zexuan::net`命名空间
2. **跨命名空间继承**：导致复杂的依赖关系
3. **平台检测逻辑分散**：在多个地方都有平台检测代码
4. **文件组织结构不清晰**：poller实现分散在不同目录
5. **内存管理不优雅**：使用原始指针而不是智能指针

## 改进方案

### 1. 正确的分层架构
```
zexuan/
├── platform/network/          # 平台相关的网络抽象
│   ├── poller.hpp            # Poller基类
│   ├── poller_impl.hpp       # 具体实现类声明
│   └── poller.cpp            # 所有实现代码
└── net/                      # 网络库
    ├── poller.hpp            # 使用platform的Poller
    └── poller.cpp            # 简单的适配层
```

### 2. 保持原有逻辑
- **默认行为**：Linux下使用epoll，其他平台使用poll
- **环境变量控制**：通过`USE_POLL`环境变量强制使用poll
- **平台检测**：Linux平台支持epoll，其他平台回退到poll

### 3. 使用现代C++特性
- 使用`std::unique_ptr`进行内存管理
- 使用`std::make_unique`创建对象
- 使用`override`关键字明确虚函数重写

### 4. 改进的API设计
```cpp
// 平台层API
namespace zexuan::platform::network {
    std::unique_ptr<Poller> Poller::createDefaultPoller();
}

// 网络层API
namespace zexuan::net {
    using Poller = zexuan::platform::network::Poller;
    std::unique_ptr<Poller> Poller::createDefaultPoller(EventLoop* loop);
}
```

### 5. 更优雅的平台抽象
- 统一的平台检测宏
- 类型别名定义
- 平台特定函数封装

## 优势

1. **正确的分层**：platform层处理平台差异，net层提供网络抽象
2. **更好的内存安全**：使用智能指针避免内存泄漏
3. **更简洁的API**：工厂方法返回智能指针
4. **更好的可维护性**：平台相关代码集中在platform层
5. **更现代的设计**：充分利用C++17特性
6. **清晰的职责分离**：platform负责平台抽象，net负责网络逻辑
7. **保持兼容性**：与原有逻辑完全一致

## 使用示例

```cpp
// 在EventLoop中自动选择最佳poller
EventLoop loop;  // 内部使用 platform::network::Poller::createDefaultPoller()

// 手动创建特定poller
auto poller = zexuan::platform::network::Poller::createDefaultPoller();

// 通过环境变量控制
// export USE_POLL=1  # 强制使用poll
```

## 兼容性

- 保持了原有的API接口
- 保持了原有的逻辑行为
- 保持了环境变量控制机制
- 向后兼容现有代码
- 正确的分层架构

## 文件结构

```
zexuan/platform/network/
├── poller.hpp          # Poller基类
├── poller_impl.hpp     # PollPoller和EPollPoller声明
└── poller.cpp          # PollPoller和EPollPoller实现

zexuan/net/
├── poller.hpp          # 使用platform的Poller
└── poller.cpp          # 简单的适配层
```

## 原有逻辑保持

### 选择逻辑
```cpp
// 保持和原来一样的逻辑：通过环境变量选择
if (::getenv("USE_POLL")) {
    return std::make_unique<PollPoller>();
} else {
#ifdef ZEXUAN_LINUX
    return std::make_unique<EPollPoller>();
#else
    return std::make_unique<PollPoller>();
#endif
}
```

### 实现细节
1. **PollPoller**：跨平台实现，使用poll系统调用
2. **EPollPoller**：Linux专用实现，使用epoll系统调用
3. **环境变量控制**：`USE_POLL`环境变量强制使用poll
4. **默认行为**：Linux下默认使用epoll，其他平台使用poll

### 常量定义
保持原有的常量定义：
- `kNew = -1`
- `kAdded = 1` 
- `kDeleted = 2`

### 错误处理
保持原有的错误处理逻辑：
- 使用`spdlog::trace`进行调试日志
- 使用`spdlog::error`和`spdlog::critical`进行错误日志
- 保持原有的assert检查
